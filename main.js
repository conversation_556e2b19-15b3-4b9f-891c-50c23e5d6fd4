export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    const auth = url.searchParams.get("auth");

    if (!auth || auth !== 'gVtR7649d75zyNAhZaFW') {
      return new Response("Unauthorized", { status: 401 });
    }

    if (path === "/list" && request.method === "GET") {
      return handleList(env);
    } else if (path === "/check" && request.method === "GET") {
      return handleHealthCheck(env);
    } else {
      return new Response("Not Found", { status: 404 });
    }
  },
};

async function handleList(env) {
  const raw = await env.PROXY_KV.get("proxy_list");
  const list = raw ? JSON.parse(raw) : [];
  return Response.json(list);
}

async function handleHealthCheck(env) {
  const raw = await env.PROXY_KV.get("proxy_list");
  const list = raw ? JSON.parse(raw) : [];

  const results = await Promise.all(
    list.map(async (proxy) => {
      const isAlive = await checkProxyAlive(proxy);
      return { proxy, isAlive };
    })
  );

  const aliveProxies = results
    .filter((item) => item.isAlive)
    .map((item) => item.proxy);

  await env.PROXY_KV.put("proxy_list", JSON.stringify(aliveProxies));

  return Response.json({
    checked: list.length,
    alive: aliveProxies.length,
    dead: list.length - aliveProxies.length,
    proxies: aliveProxies,
  });
}


async function checkProxyAlive(proxy) {
  const endpoint = `https://ese2q5mf32zwlmpashqbbpqw6q0xtsuj.lambda-url.ap-east-1.on.aws/proxycheck?proxy=${encodeURIComponent(proxy)}&auth=HysvALNx3mHV0MiHhFR5`;

  try {
    const res = await fetch(endpoint);
    if (!res.ok) return false;

    const json = await res.json();
    return json.alive === true;
  } catch {
    return false;
  }
}




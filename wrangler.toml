name = "checkproxy"
main = "./main.js"
compatibility_date = "2025-07-01"

routes = [
    { pattern = "checkproxy.mihoyo.cn.com", custom_domain = true }
]
node_compat = true
logpush = false
usage_model = "bundled"

rules = [
  { type = "ESModule", globs = ["**/*.js"], fallthrough = true }
]

kv_namespaces = [
    { binding = "PROXY_KV", id = "6e66edda006844f29a04c233e2d639c4" }

]


# reference: https://developers.cloudflare.com/workers/